#include "bsp_usart.h"
#include "stdio.h"


#include "stdio.h"

//重定向fputc函数
int fputc(int ch, FILE *stream)
{
    while( DL_UART_isBusy(UART_0_INST) == true );
    DL_UART_Main_transmitData(UART_0_INST, ch);
    return ch;
}

//重定向fputs函数
int fputs(const char* restrict s, FILE* restrict stream) {

    uint16_t char_len=0;
    while(*s!=0)
    {
        while( DL_UART_isBusy(UART_0_INST) == true );
        DL_UART_Main_transmitData(UART_0_INST, *s++);
        char_len++;
    }
    return char_len;
}
int puts(const char* _ptr)
{
 return 0;
}

uint8_t uart_rx_buffer[128] = {0};
uint8_t uart_rx_index = 0;
uint8_t uart_rx_ticks = 0;

//串口0发送单个字符
void uart0_send_char(char ch)
{
    //当串口0忙的时候等待，不忙的时候再发送传进来的字符
    while( DL_UART_isBusy(UART_0_INST) == true );
    //发送单个字符
    DL_UART_Main_transmitData(UART_0_INST, ch);
}
//串口0发送字符串
void uart0_send_string(char* str)
{
    //当前字符串地址不在结尾 并且 字符串首地址不为空
    while(*str!=0&&str!=0)
    {
        //发送字符串首地址中的字符，并且在发送完成之后首地址自增
        uart0_send_char(*str++);
    }
}

void uart0_init(void)
{
    //清除串口中断标志
    NVIC_ClearPendingIRQ(UART_0_INST_INT_IRQN);
    //使能串口中断
    NVIC_EnableIRQ(UART_0_INST_INT_IRQN);
}

//串口的中断服务函数
void UART_0_INST_IRQHandler(void)
{
    //如果产生了串口中断
    switch( DL_UART_getPendingInterrupt(UART_0_INST) )
    {
        //串口中断+超时解析
        case DL_UART_IIDX_RX://如果是接收中断
            uart_rx_ticks = get_systicks();
            //将发送过来的数据保存在变量中
            uart_rx_buffer[uart_rx_index++] = DL_UART_Main_receiveData(UART_0_INST);
            break;

        default://其他的串口中断
            break;
    }
}

void uart_proc(void)
{
    
     // 如果接收索引为0，说明没有数据需要处理，直接返回
     if(uart_rx_index == 0) return;

     // 如果从最后一次接收到数据到现在已经超过100ms
     if(get_systicks() - uart_rx_ticks > 100) // 100ms内没有收到新的数据
     {
         printf("uart data: %s\n", uart_rx_buffer);

         // 清空接收缓冲区，将接收索引置零
         memset(uart_rx_buffer, 0, uart_rx_index);
         uart_rx_index = 0;
     }
}
