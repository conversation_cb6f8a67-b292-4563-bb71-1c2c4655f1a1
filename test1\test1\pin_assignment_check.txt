MSPM0G3507 引脚分配检查表
=============================

现有功能引脚分配：
PA10: UART TX
PA11: UART RX  
PA12: AIN2 (现有GPIO)
PA13: AIN1 (现有GPIO)
PA19: SWDIO (调试)
PA20: SWCLK (调试)
PA27: ADC输入
PB0:  灰度地址0
PB1:  灰度地址1
PB2:  灰度地址2
PB22: LED

新增电机驱动引脚分配：
PA14: PWM_MOTOR_C0 (左电机PWM)
PA15: PWM_MOTOR_C1 (右电机PWM)
PB3:  GPIO_MOTOR_AIN1 (左电机方向1)
PB4:  GPIO_MOTOR_AIN2 (左电机方向2)
PB5:  GPIO_MOTOR_BIN1 (右电机方向1)
PB6:  GPIO_MOTOR_BIN2 (右电机方向2)
PB7:  ENCODER1A (左编码器A相输入捕获)
PB8:  ENCODER2A (右编码器A相输入捕获)
PB9:  ENCODER1B (左编码器B相GPIO输入)
PB10: ENCODER2B (右编码器B相GPIO输入)

引脚冲突检查：
✓ PA14: 未使用，可分配给PWM
✓ PA15: 未使用，可分配给PWM
✓ PB3:  未使用，可分配给电机方向控制
✓ PB4:  未使用，可分配给电机方向控制
✓ PB5:  未使用，可分配给电机方向控制
✓ PB6:  未使用，可分配给电机方向控制
✓ PB7:  未使用，可分配给编码器输入捕获
✓ PB8:  未使用，可分配给编码器输入捕获
✓ PB9:  未使用，可分配给编码器GPIO输入
✓ PB10: 未使用，可分配给编码器GPIO输入

定时器资源分配：
TIMG0: PWM_MOTOR (PWM输出)
TIMA0: ENCODER1A (输入捕获)
TIMA1: TIMER_CONTROL (控制周期定时器)
TIMG6: ENCODER2A (输入捕获)

中断优先级分配：
0: SysTick (最高优先级)
1: UART, TIMER_CONTROL
2: ENCODER1A, ENCODER2A

结论：
✓ 所有引脚分配无冲突
✓ 定时器资源分配合理
✓ 中断优先级设置合理
✓ 硬件配置满足MG513X电机驱动需求
